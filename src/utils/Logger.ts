import { DBOS } from '@dbos-inc/dbos-sdk';
import fs from 'fs';
import path from 'path';

/**
 * Central logger that switches between DBOS.logger and file logging based on quiet mode
 */
export class Logger {
  private static instance: Logger;
  private quietMode: boolean = false;
  private logStream: fs.WriteStream | null = null;
  private logFilePath: string;
  private originalConsole: any = null;

  private constructor() {
    this.logFilePath = path.join(process.cwd(), 'server.log');
  }

  /**
   * Get the singleton logger instance
   */
  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * Initialize the logger with quiet mode setting
   */
  initialize(quietMode: boolean = false): void {
    // Use process.stdout.write to ensure this message appears even if console is overridden
    process.stdout.write(`🔧 Logger.initialize() called with quietMode: ${quietMode}\n`);
    this.quietMode = quietMode;

    if (this.quietMode && !this.logStream) {
      process.stdout.write(`🔧 Setting up file logging and console redirection...\n`);
      this.setupFileLogging();
      this.setupConsoleRedirection();
      process.stdout.write(`🔧 File logging and console redirection setup complete\n`);
    } else {
      process.stdout.write(`🔧 Logger initialized in non-quiet mode\n`);
    }
  }

  /**
   * Set up file logging for quiet mode
   */
  private setupFileLogging(): void {
    // Create the log file stream
    this.logStream = fs.createWriteStream(this.logFilePath, { flags: 'a' });

    // Write initialization message
    this.writeToFile('SYSTEM', 'Central logger initialized in quiet mode');
  }

  /**
   * Set up console redirection to file for quiet mode
   */
  private setupConsoleRedirection(): void {
    // Store original console methods
    this.originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info
    };

    // Helper function to write to file with proper formatting
    const writeToFile = (level: string, ...args: any[]) => {
      if (!this.logStream) return;

      const timestamp = new Date().toISOString();
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ');

      const logEntry = `[${timestamp}] [${level}] ${message}\n`;
      this.logStream.write(logEntry);
    };

    // Override console methods to write to file instead of stdout/stderr
    console.log = (...args: any[]) => writeToFile('INFO', ...args);
    console.error = (...args: any[]) => writeToFile('ERROR', ...args);
    console.warn = (...args: any[]) => writeToFile('WARN', ...args);
    console.info = (...args: any[]) => writeToFile('INFO', ...args);
  }

  /**
   * Helper function to write to file with timestamp and formatting
   */
  private writeToFile(level: string, message: string, ...args: any[]): void {
    if (!this.logStream) return;

    const timestamp = new Date().toISOString();
    const formattedArgs = args.map(arg =>
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ');
    
    const fullMessage = args.length > 0 
      ? `${message} ${formattedArgs}`
      : message;
    
    const logEntry = `[${timestamp}] [${level}] ${fullMessage}\n`;
    this.logStream.write(logEntry);
  }

  /**
   * Log info level message
   */
  info(message: string, ...args: any[]): void {
    if (this.quietMode) {
      this.writeToFile('INFO', message, ...args);
    } else {
      // Use DBOS logger if available, fallback to console
      try {
        if (args.length > 0) {
          DBOS.logger.info(`${message} ${args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
          ).join(' ')}`);
        } else {
          DBOS.logger.info(message);
        }
      } catch (error) {
        // Fallback to console if DBOS logger is not available
        console.log(`[INFO] ${message}`, ...args);
      }
    }
  }

  /**
   * Log warning level message
   */
  warn(message: string, ...args: any[]): void {
    if (this.quietMode) {
      this.writeToFile('WARN', message, ...args);
    } else {
      try {
        if (args.length > 0) {
          DBOS.logger.warn(`${message} ${args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
          ).join(' ')}`);
        } else {
          DBOS.logger.warn(message);
        }
      } catch (error) {
        console.warn(`[WARN] ${message}`, ...args);
      }
    }
  }

  /**
   * Log error level message
   */
  error(message: string, error?: Error | any, ...args: any[]): void {
    const errorMessage = error instanceof Error 
      ? `${message}: ${error.message}` 
      : error 
        ? `${message}: ${String(error)}`
        : message;

    if (this.quietMode) {
      this.writeToFile('ERROR', errorMessage, ...args);
      // Also log stack trace if available
      if (error instanceof Error && error.stack) {
        this.writeToFile('ERROR', `Stack trace: ${error.stack}`);
      }
    } else {
      try {
        if (args.length > 0) {
          DBOS.logger.error(`${errorMessage} ${args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
          ).join(' ')}`);
        } else {
          DBOS.logger.error(errorMessage);
        }
      } catch (dbosError) {
        console.error(`[ERROR] ${errorMessage}`, ...args);
        if (error instanceof Error && error.stack) {
          console.error(`Stack trace: ${error.stack}`);
        }
      }
    }
  }

  /**
   * Log debug level message
   */
  debug(message: string, ...args: any[]): void {
    if (this.quietMode) {
      this.writeToFile('DEBUG', message, ...args);
    } else {
      try {
        if (args.length > 0) {
          DBOS.logger.debug(`${message} ${args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
          ).join(' ')}`);
        } else {
          DBOS.logger.debug(message);
        }
      } catch (error) {
        // Debug logs are less critical, so we can skip console fallback
        // to avoid noise in non-quiet mode
      }
    }
  }

  /**
   * Check if logger is in quiet mode
   */
  isQuietMode(): boolean {
    return this.quietMode;
  }

  /**
   * Get the log file path
   */
  getLogFilePath(): string {
    return this.logFilePath;
  }

  /**
   * Restore original console methods (for cleanup)
   */
  restoreConsole(): void {
    if (this.originalConsole) {
      console.log = this.originalConsole.log;
      console.error = this.originalConsole.error;
      console.warn = this.originalConsole.warn;
      console.info = this.originalConsole.info;
      this.originalConsole = null;
    }
  }

  /**
   * Close the log stream (for cleanup)
   */
  close(): void {
    this.restoreConsole();
    if (this.logStream) {
      this.logStream.end();
      this.logStream = null;
    }
  }
}

// Export a singleton instance for easy access
export const logger = Logger.getInstance();

// Export default for convenience
export default logger;
